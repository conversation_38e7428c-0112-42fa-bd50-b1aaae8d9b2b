package com.snct.typhoon.controller;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.typhoon.service.TyphoonWarningService;
import com.snct.typhoon.test.TyphoonWarningTest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 台风预警控制器
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@RestController
@RequestMapping("/typhoon/warning")
public class TyphoonWarningController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(TyphoonWarningController.class);
    
    @Autowired
    private TyphoonWarningService typhoonWarningService;
    
    @Autowired
    private TyphoonWarningTest typhoonWarningTest;
    
    /**
     * 手动触发台风预警检查
     */
    @PostMapping("/test/check")
    public AjaxResult manualCheck() {
        try {
            logger.info("手动触发台风预警检查");
            typhoonWarningService.checkAndSaveTyphoonWarnings();
            return AjaxResult.success("台风预警检查完成");
        } catch (Exception e) {
            logger.error("手动台风预警检查失败", e);
            return AjaxResult.error("台风预警检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试Redis GEO API
     */
    @GetMapping("/test/geo")
    public AjaxResult testGeoApi() {
        try {
            logger.info("测试Redis GEO API");
            typhoonWarningTest.testGeoRadius();
            return AjaxResult.success("Redis GEO API测试完成，请查看日志");
        } catch (Exception e) {
            logger.error("Redis GEO API测试失败", e);
            return AjaxResult.error("Redis GEO API测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加测试数据
     */
    @PostMapping("/test/addData")
    public AjaxResult addTestData() {
        try {
            logger.info("添加测试船舶数据");
            typhoonWarningTest.addTestShipData();
            return AjaxResult.success("测试数据添加完成");
        } catch (Exception e) {
            logger.error("添加测试数据失败", e);
            return AjaxResult.error("添加测试数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理测试数据
     */
    @PostMapping("/test/cleanData")
    public AjaxResult cleanTestData() {
        try {
            logger.info("清理测试数据");
            typhoonWarningTest.cleanTestData();
            return AjaxResult.success("测试数据清理完成");
        } catch (Exception e) {
            logger.error("清理测试数据失败", e);
            return AjaxResult.error("清理测试数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取台风预警统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            // 这里可以实现预警统计功能
            return AjaxResult.success("预警统计功能待实现");
        } catch (Exception e) {
            logger.error("获取预警统计失败", e);
            return AjaxResult.error("获取预警统计失败: " + e.getMessage());
        }
    }
}
