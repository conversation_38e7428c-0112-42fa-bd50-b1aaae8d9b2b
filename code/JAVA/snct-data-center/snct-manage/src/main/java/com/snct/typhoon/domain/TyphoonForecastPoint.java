package com.snct.typhoon.domain;

import com.snct.common.core.domain.BaseEntity;

/**
 * @ClassName: TyphoonForecastPoint
 * @Description: 台风预测路径点位
 * @author: wzewei
 * @date: 2025-08-19 16:40:44
 */
public class TyphoonForecastPoint extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 台风编号
     */
    private String tfid;

    /**
     * 台风预测版本 ID
     */
    private Long typhoonForecastId;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    /**
     * 风力
     */
    private String power;

    /**
     * 压力
     */
    private String pressure;

    /**
     * 速度
     */
    private String speed;

    /**
     * 强度
     */
    private String strong;

    /**
     * 时间
     */
    private String time;

    private Long timeStamp;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfid() {
        return tfid;
    }

    public void setTfid(String tfid) {
        this.tfid = tfid;
    }

    public Long getTyphoonForecastId() {
        return typhoonForecastId;
    }

    public void setTyphoonForecastId(Long typhoonForecastId) {
        this.typhoonForecastId = typhoonForecastId;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getPressure() {
        return pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }
}
