package com.snct.typhoon.domain.vo;

import com.snct.common.utils.bean.BeanUtils;
import com.snct.typhoon.domain.Typhoon;

/**
 * @ClassName: TyphoonVo
 * @Description: 台风视图类
 * @author: wzewei
 * @date: 2025-08-19 16:39:52
 */
public class TyphoonVo {

    /**
     * 台风编号
     */
    private String tfid;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 中文名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String enname;

    /**
     * 是否活跃
     */
    private String isactive;

    /**
     * 开始时间
     */
    private String starttime;
    /**
     * 开始时间戳
     */
    private Long startTimeStamp;

    /**
     * 结束时间
     */
    private String endtime;
    /**
     * 结束时间戳
     */
    private Long endTimeStamp;

    /**
     * 警告级别
     */
    private String warnlevel;


    /**
     * 包装类转对象
     *
     * @param typhoonVo
     * @return
     */
    public static Typhoon voToObj(TyphoonVo typhoonVo) {
        if (typhoonVo == null) {
            return null;
        }
        Typhoon typhoon = new Typhoon();
        BeanUtils.copyBeanProp(typhoon, typhoonVo);
        return typhoon;
    }

    /**
     * 对象转包装类
     *
     * @param typhoon
     * @return
     */
    public static TyphoonVo objToVo(Typhoon typhoon) {
        if (typhoon == null) {
            return null;
        }
        TyphoonVo typhoonVo = new TyphoonVo();
        BeanUtils.copyBeanProp(typhoonVo, typhoon);
        return typhoonVo;
    }

    public String getTfid() {
        return tfid;
    }

    public void setTfid(String tfid) {
        this.tfid = tfid;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnname() {
        return enname;
    }

    public void setEnname(String enname) {
        this.enname = enname;
    }

    public String getIsactive() {
        return isactive;
    }

    public void setIsactive(String isactive) {
        this.isactive = isactive;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getWarnlevel() {
        return warnlevel;
    }

    public void setWarnlevel(String warnlevel) {
        this.warnlevel = warnlevel;
    }

    public Long getStartTimeStamp() {
        return startTimeStamp;
    }

    public void setStartTimeStamp(Long startTimeStamp) {
        this.startTimeStamp = startTimeStamp;
    }

    public Long getEndTimeStamp() {
        return endTimeStamp;
    }

    public void setEndTimeStamp(Long endTimeStamp) {
        this.endTimeStamp = endTimeStamp;
    }
}
