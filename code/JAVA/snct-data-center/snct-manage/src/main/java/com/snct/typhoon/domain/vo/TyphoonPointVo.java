package com.snct.typhoon.domain.vo;

import com.snct.common.utils.bean.BeanUtils;
import com.snct.typhoon.domain.TyphoonForecast;
import com.snct.typhoon.domain.TyphoonPoint;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: TyphoonPointVo
 * @Description: 台风点信息视图类
 * @author: wzewei
 * @date: 2025-08-19 16:41:58
 */
public class TyphoonPointVo {

    /**
     * 预测
     */
    private List<TyphoonForecastVo> forecast;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    /**
     * 移动方向
     */
    private String movedirection;

    /**
     * 移动速度
     */
    private String movespeed;

    /**
     * 风力
     */
    private String power;

    /**
     * 大气压
     */
    private String pressure;

    /**
     * 半径
     */
    private String radius7;

    /**
     * 半径
     */
    private String radius10;

    /**
     * 半径
     */
    private String radius12;

    /**
     * 速度
     */
    private String speed;

    /**
     * 强度
     */
    private String strong;

    /**
     * 时间
     */
    private String time;

    private Long timeStamp;

    private String ckposition;

    private String jl;

    /**
     * 包装类转对象
     *
     * @param typhoonPointVo
     * @return
     */
    public static TyphoonPoint voToObj(TyphoonPointVo typhoonPointVo) {
        if (typhoonPointVo == null) {
            return null;
        }
        TyphoonPoint typhoonPoint = new TyphoonPoint();
        BeanUtils.copyBeanProp(typhoonPoint, typhoonPointVo);
        List<TyphoonForecastVo> forecastVoList = typhoonPointVo.getForecast();
        if (forecastVoList != null && !forecastVoList.isEmpty()) {
            List<TyphoonForecast> forecastList = new ArrayList<>();
            for (TyphoonForecastVo typhoonForecastVo : forecastVoList) {
                TyphoonForecast typhoonForecast = TyphoonForecastVo.voToObj(typhoonForecastVo);
                forecastList.add(typhoonForecast);
            }
            typhoonPoint.setForecast(forecastList);
        }
        return typhoonPoint;
    }

    /**
     * 对象转包装类
     *
     * @param typhoonPoint
     * @return
     */
    public static TyphoonPointVo objToVo(TyphoonPoint typhoonPoint) {
        if (typhoonPoint == null) {
            return null;
        }
        TyphoonPointVo typhoonPointVo = new TyphoonPointVo();
        BeanUtils.copyBeanProp(typhoonPointVo, typhoonPoint);
        return typhoonPointVo;
    }

    public List<TyphoonForecastVo> getForecast() {
        return forecast;
    }

    public void setForecast(List<TyphoonForecastVo> forecast) {
        this.forecast = forecast;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getMovedirection() {
        return movedirection;
    }

    public void setMovedirection(String movedirection) {
        this.movedirection = movedirection;
    }

    public String getMovespeed() {
        return movespeed;
    }

    public void setMovespeed(String movespeed) {
        this.movespeed = movespeed;
    }

    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getPressure() {
        return pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getRadius7() {
        return radius7;
    }

    public void setRadius7(String radius7) {
        this.radius7 = radius7;
    }

    public String getRadius10() {
        return radius10;
    }

    public void setRadius10(String radius10) {
        this.radius10 = radius10;
    }

    public String getRadius12() {
        return radius12;
    }

    public void setRadius12(String radius12) {
        this.radius12 = radius12;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getCkposition() {
        return ckposition;
    }

    public void setCkposition(String ckposition) {
        this.ckposition = ckposition;
    }

    public String getJl() {
        return jl;
    }

    public void setJl(String jl) {
        this.jl = jl;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }
}
