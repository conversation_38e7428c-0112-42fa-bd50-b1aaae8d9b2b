package com.snct.typhoon.mapper;

import com.snct.typhoon.domain.Typhoon;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 台风 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface TyphoonMapper {

    /**
     *
     * 查询台风信息
     *
     * @param typhoon 台风信息
     * @return 台风信息
     */
    public Typhoon selectTyphoon(Typhoon typhoon);

    /**
     * 查询台风列表
     *
     * @param typhoon 台风信息
     * @return 台风集合
     */
    public List<Typhoon> selectTyphoonList(Typhoon typhoon);

    /**
     * 新增台风
     *
     * @param typhoon 台风信息
     * @return 结果
     */
    public int addTyphoon(Typhoon typhoon);

    /**
     * 修改台风
     *
     * @param typhoon 台风信息
     * @return 结果
     */
    public int updateTyphoon(Typhoon typhoon);

}