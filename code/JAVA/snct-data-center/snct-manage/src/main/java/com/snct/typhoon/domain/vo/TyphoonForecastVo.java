package com.snct.typhoon.domain.vo;

import com.snct.common.utils.bean.BeanUtils;
import com.snct.typhoon.domain.TyphoonForecast;
import com.snct.typhoon.domain.TyphoonForecastPoint;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: TyphoonForecast
 * @Description: 台风点位预测
 * @author: wzewei
 * @date: 2025-08-19 16:40:15
 */
public class TyphoonForecastVo {

    /**
     * 预测的点位
     */
    private List<TyphoonForecastPointVo> forecastPoints;

    /**
     * 来源
     */
    private String tm;


    /**
     * 包装类转对象
     * @param typhoonForecastVo
     * @return
     */
    public static TyphoonForecast voToObj(TyphoonForecastVo typhoonForecastVo) {
        if (typhoonForecastVo == null){
            return null;
        }
        TyphoonForecast typhoonForecast = new TyphoonForecast();
        BeanUtils.copyBeanProp(typhoonForecast, typhoonForecastVo);

        List<TyphoonForecastPointVo> forecastPointVoList = typhoonForecastVo.getForecastPoints();
        if (forecastPointVoList != null && !forecastPointVoList.isEmpty()){
            List<TyphoonForecastPoint> forecastPointList = new ArrayList<>();
            for (TyphoonForecastPointVo forecastPointVo : forecastPointVoList){
                forecastPointList.add(TyphoonForecastPointVo.voToObj(forecastPointVo));
            }
            typhoonForecast.setForecastPoints(forecastPointList);
        }
        return typhoonForecast;
    }

    /**
     * 对象转包装类
     * @param TyphoonForecast
     * @return
     */
    public static TyphoonForecastVo objToVo(TyphoonForecast TyphoonForecast) {
        if (TyphoonForecast == null){
            return null;
        }
        TyphoonForecastVo TyphoonForecastVo = new TyphoonForecastVo();
        BeanUtils.copyBeanProp(TyphoonForecastVo, TyphoonForecast);
        return TyphoonForecastVo;
    }

    public List<TyphoonForecastPointVo> getForecastPoints() {
        return forecastPoints;
    }

    public void setForecastPoints(List<TyphoonForecastPointVo> forecastPoints) {
        this.forecastPoints = forecastPoints;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }
}
