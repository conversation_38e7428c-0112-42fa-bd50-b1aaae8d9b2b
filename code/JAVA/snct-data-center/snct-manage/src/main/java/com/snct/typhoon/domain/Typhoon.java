package com.snct.typhoon.domain;

import com.snct.common.core.domain.BaseEntity;

/**
 * @ClassName: Typhoon
 * @Description: 台风
 * @author: wzewei
 * @date: 2025-08-19 16:39:52
 */
public class Typhoon extends BaseEntity {
    private static final long serialVersionUID = 1L;


    private Long id;
    /**
     * 台风编号
     */
    private String tfid;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 中文名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String enname;

    /**
     * 是否活跃
     */
    private String isactive;

    /**
     * 开始时间
     */
    private String starttime;

    /**
     * 开始时间戳
     */
    private Long startTimeStamp;

    /**
     * 结束时间
     */
    private String endtime;
    /**
     * 结束时间戳
     */
    private Long endTimeStamp;

    /**
     * 警告级别
     */
    private String warnlevel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfid() {
        return tfid;
    }

    public void setTfid(String tfid) {
        this.tfid = tfid;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnname() {
        return enname;
    }

    public void setEnname(String enname) {
        this.enname = enname;
    }

    public String getIsactive() {
        return isactive;
    }

    public void setIsactive(String isactive) {
        this.isactive = isactive;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getWarnlevel() {
        return warnlevel;
    }

    public void setWarnlevel(String warnlevel) {
        this.warnlevel = warnlevel;
    }

    public Long getStartTimeStamp() {
        return startTimeStamp;
    }

    public void setStartTimeStamp(Long startTimeStamp) {
        this.startTimeStamp = startTimeStamp;
    }

    public Long getEndTimeStamp() {
        return endTimeStamp;
    }

    public void setEndTimeStamp(Long endTimeStamp) {
        this.endTimeStamp = endTimeStamp;
    }
}
