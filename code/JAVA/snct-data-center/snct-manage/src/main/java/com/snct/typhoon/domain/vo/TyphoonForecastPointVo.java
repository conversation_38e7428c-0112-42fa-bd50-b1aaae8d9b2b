package com.snct.typhoon.domain.vo;

import com.snct.common.utils.bean.BeanUtils;
import com.snct.typhoon.domain.TyphoonForecastPoint;

/**
 * @ClassName: TyphoonForecastPoint
 * @Description: 台风预测路径点位
 * @author: wzewei
 * @date: 2025-08-19 16:40:44
 */
public class TyphoonForecastPointVo {

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    /**
     * 风力
     */
    private String power;

    /**
     * 压力
     */
    private String pressure;

    /**
     * 速度
     */
    private String speed;

    /**
     * 强度
     */
    private String strong;

    /**
     * 时间
     */
    private String time;

    private Long timeStamp;


    /**
     * 包装类转对象
     * @param TyphoonForecastPointVo
     * @return
     */
    public static TyphoonForecastPoint voToObj(TyphoonForecastPointVo TyphoonForecastPointVo) {
        if (TyphoonForecastPointVo == null){
            return null;
        }
        TyphoonForecastPoint TyphoonForecastPoint = new TyphoonForecastPoint();
        BeanUtils.copyBeanProp(TyphoonForecastPoint, TyphoonForecastPointVo);
        return TyphoonForecastPoint;
    }

    /**
     * 对象转包装类
     * @param TyphoonForecastPoint
     * @return
     */
    public static TyphoonForecastPointVo objToVo(TyphoonForecastPoint TyphoonForecastPoint) {
        if (TyphoonForecastPoint == null){
            return null;
        }
        TyphoonForecastPointVo TyphoonForecastPointVo = new TyphoonForecastPointVo();
        BeanUtils.copyBeanProp(TyphoonForecastPointVo, TyphoonForecastPoint);
        return TyphoonForecastPointVo;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getPressure() {
        return pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }
}
