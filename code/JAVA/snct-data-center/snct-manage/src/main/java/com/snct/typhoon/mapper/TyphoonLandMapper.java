package com.snct.typhoon.mapper;


import com.snct.typhoon.domain.TyphoonLand;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 台风登录地 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface TyphoonLandMapper {

    /**
     *
     * 查询台风登陆地址
     *
     * @param typhoonLand 台风登陆地址
     * @return 台风信息
     */
    public TyphoonLand selectTyphoonLand(TyphoonLand typhoonLand);

    /**
     * 查询台风登陆地址
     *
     * @param typhoonLand 台风登陆地址
     * @return 台风集合
     */
    public List<TyphoonLand> selectTyphoonLandList(TyphoonLand typhoonLand);

    /**
     * 新增台风登陆地址
     *
     * @param typhoonLand 台风登陆地址
     * @return 结果
     */
    public int addTyphoonLand(TyphoonLand typhoonLand);

    /**
     * 修改台风登陆地址
     *
     * @param typhoonLand 台风登陆地址
     * @return 结果
     */
    public int updateTyphoonLand(TyphoonLand typhoonLand);

}