package com.snct.typhoon.scheduler;

import com.snct.typhoon.service.TyphoonWarningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 台风预警定时任务调度器
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Component
@ConditionalOnProperty(name = "typhoon.warning.scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class TyphoonWarningScheduler {
    
    private static final Logger logger = LoggerFactory.getLogger(TyphoonWarningScheduler.class);
    
    @Autowired
    private TyphoonWarningService typhoonWarningService;
    
    /**
     * 定时台风预警检查
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600,000毫秒
    public void scheduledTyphoonWarningCheck() {
        logger.info("开始执行定时台风预警检查");
        
        long startTime = System.currentTimeMillis();
        
        try {
            typhoonWarningService.checkAndSaveTyphoonWarnings();
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("定时台风预警检查完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error("定时台风预警检查失败，耗时: {}ms", duration, e);
        }
    }
    
    /**
     * 定时清理过期预警记录
     * 每天凌晨2点执行一次
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void scheduledCleanExpiredWarnings() {
        logger.info("开始执行定时清理过期预警记录");
        
        long startTime = System.currentTimeMillis();
        
        try {
            typhoonWarningService.cleanExpiredWarnings();
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("定时清理过期预警记录完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error("定时清理过期预警记录失败，耗时: {}ms", duration, e);
        }
    }
    
    /**
     * 手动触发台风预警检查（用于测试或紧急情况）
     */
    public void manualTriggerWarningCheck() {
        logger.info("手动触发台风预警检查");
        
        try {
            typhoonWarningService.checkAndSaveTyphoonWarnings();
            logger.info("手动台风预警检查完成");
            
        } catch (Exception e) {
            logger.error("手动台风预警检查失败", e);
            throw new RuntimeException("手动台风预警检查失败", e);
        }
    }
}
