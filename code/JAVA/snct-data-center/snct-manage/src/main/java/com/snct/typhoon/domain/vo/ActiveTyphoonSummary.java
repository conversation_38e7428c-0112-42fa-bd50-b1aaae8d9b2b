package com.snct.typhoon.domain.vo;

/**
 * @ClassName: ActiveTyphoonSummary
 * @Description: 活跃台风摘要信息
 * @author: wzewei
 * @date: 2025-08-20 16:00:00
 */
public class ActiveTyphoonSummary {

    /**
     * 台风编号
     */
    private String tfid;

    /**
     * 台风名称
     */
    private String name;

    /**
     * 中心纬度
     */
    private String centerLat;

    /**
     * 中心经度
     */
    private String centerLng;

    /**
     * 大气压
     */
    private String pressure;

    /**
     * 速度
     */
    private String speed;

    /**
     * 台风强度等级
     */
    private String strong;

    /**
     * 移动方向
     */
    private String movedirection;

    /**
     * 移动速度
     */
    private String movespeed;

    /**
     * 风力等级
     */
    private String power;


    /**
     * 位置更新时间
     */
    private String updateTime;

    public ActiveTyphoonSummary() {
    }

    public ActiveTyphoonSummary(String tfid, String name, String centerLat, String centerLng,
                               String pressure, String speed, String strong, String movedirection,
                               String movespeed, String power, String updateTime) {
        this.tfid = tfid;
        this.name = name;
        this.centerLat = centerLat;
        this.centerLng = centerLng;
        this.pressure = pressure;
        this.speed = speed;
        this.strong = strong;
        this.movedirection = movedirection;
        this.movespeed = movespeed;
        this.power = power;
        this.updateTime = updateTime;
    }

    public String getTfid() {
        return tfid;
    }

    public void setTfid(String tfid) {
        this.tfid = tfid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCenterLat() {
        return centerLat;
    }

    public void setCenterLat(String centerLat) {
        this.centerLat = centerLat;
    }

    public String getCenterLng() {
        return centerLng;
    }

    public void setCenterLng(String centerLng) {
        this.centerLng = centerLng;
    }

    public String getPressure() {
        return pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }

    public String getMovedirection() {
        return movedirection;
    }

    public void setMovedirection(String movedirection) {
        this.movedirection = movedirection;
    }

    public String getMovespeed() {
        return movespeed;
    }

    public void setMovespeed(String movespeed) {
        this.movespeed = movespeed;
    }

    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ActiveTyphoonSummary{" +
                "tfid='" + tfid + '\'' +
                ", name='" + name + '\'' +
                ", centerLat='" + centerLat + '\'' +
                ", centerLng='" + centerLng + '\'' +
                ", pressure='" + pressure + '\'' +
                ", speed='" + speed + '\'' +
                ", strong='" + strong + '\'' +
                ", movedirection='" + movedirection + '\'' +
                ", movespeed='" + movespeed + '\'' +
                ", power='" + power + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }
}
