package com.snct.typhoon.domain;

import com.snct.common.core.domain.BaseEntity;

/**
 * @ClassName: TyphoonLand
 * @Description: 台风登陆信息
 * @author: wzewei
 * @date: 2025-08-19 16:41:38
 */
public class TyphoonLand extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 台风编号
     */
    private String tfid;

    /**
     * 消息
     */
    private String info;

    /**
     * 登陆地址
     */
    private String landaddress;

    /**
     * 登陆时间
     */
    private String landtime;

    private Long landTimeStamp;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    /**
     * 强度
     */
    private String strong;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTfid() {
        return tfid;
    }

    public void setTfid(String tfid) {
        this.tfid = tfid;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getLandaddress() {
        return landaddress;
    }

    public void setLandaddress(String landaddress) {
        this.landaddress = landaddress;
    }

    public String getLandtime() {
        return landtime;
    }

    public void setLandtime(String landtime) {
        this.landtime = landtime;
    }

    public Long getLandTimeStamp() {
        return landTimeStamp;
    }

    public void setLandTimeStamp(Long landTimeStamp) {
        this.landTimeStamp = landTimeStamp;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getStrong() {
        return strong;
    }

    public void setStrong(String strong) {
        this.strong = strong;
    }
}
