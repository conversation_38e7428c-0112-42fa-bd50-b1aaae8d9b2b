<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.snct.typhoon.mapper.TyphoonMapper">

    <resultMap type="Typhoon" id="TyphoonResult">
        <id property="id" column="id"/>
        <result property="tfid" column="tfid"/>
        <result property="year" column="year"/>
        <result property="name" column="name"/>
        <result property="enname" column="enname"/>
        <result property="isactive" column="isactive"/>
        <result property="starttime" column="starttime"/>
        <result property="startTimeStamp" column="start_time_stamp"/>
        <result property="endtime" column="endtime"/>
        <result property="endTimeStamp" column="end_time_stamp"/>
        <result property="warnlevel" column="warnlevel"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTyphoonVo">
        select id, tfid, year, name, enname, isactive, starttime, start_time_stamp, endtime, end_time_stamp, warnlevel, create_time, update_time
		from typhoon
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="tfid !=null">
                and tfid = #{tfid}
            </if>
        </where>
    </sql>

    <select id="selectTyphoon" parameterType="Typhoon" resultMap="TyphoonResult">
        <include refid="selectTyphoonVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectTyphoonList" parameterType="Typhoon" resultMap="TyphoonResult">
        <include refid="selectTyphoonVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="year !=null and year != ''">
                and year = #{year}
            </if>
            <if test="isactive !=null and isactive != ''">
                and isactive = #{isactive}
            </if>
        </where>
    </select>

    <insert id="addTyphoon" parameterType="Typhoon">
        insert into typhoon (
        <if test="tfid != null and tfid != '' ">tfid,</if>
        <if test="year != null and year != '' ">year,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="enname != null and enname != ''">enname,</if>
        <if test="isactive != null and enname != ''">isactive,</if>
        <if test="starttime != null and starttime != ''">starttime,</if>
        <if test="startTimeStamp != null and startTimeStamp != ''">start_time_stamp,</if>
        <if test="endtime != null and endtime != ''">endtime,</if>
        <if test="endTimeStamp != null and endTimeStamp != ''">end_time_stamp,</if>
        <if test="warnlevel != null and warnlevel != ''">warnlevel,</if>
        create_time
        )values(
        <if test="tfid != null and tfid != ''">#{tfid},</if>
        <if test="year != null and year != ''">#{year},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="enname != null and enname != ''">#{enname},</if>
        <if test="isactive != null and isactive != ''">#{isactive},</if>
        <if test="starttime != null and starttime != ''">#{starttime},</if>
        <if test="startTimeStamp != null and startTimeStamp != ''">#{startTimeStamp},</if>
        <if test="endtime != null and endtime != ''">#{endtime},</if>
        <if test="endTimeStamp != null and endTimeStamp != ''">#{endTimeStamp},</if>
        <if test="warnlevel != null and warnlevel != ''">#{warnlevel},</if>
        sysdate()
        )
    </insert>

    <update id="updateTyphoon" parameterType="Typhoon">
        update typhoon
        <set>
            <if test="tfid != null and tfid != ''">tfid = #{tfid},</if>
            <if test="year != null and year != ''">year = #{year},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="enname != null and enname != ''">enname = #{enname},</if>
            <if test="isactive != null and isactive != ''">isactive = #{isactive},</if>
            <if test="starttime != null and starttime != ''">starttime = #{starttime},</if>
            <if test="startTimeStamp != null and startTimeStamp != ''">start_time_stamp = #{startTimeStamp},</if>
            <if test="endtime != null and endtime != ''">endtime = #{endtime},</if>
            <if test="endTimeStamp != null and endTimeStamp != ''">end_time_stamp = #{endTimeStamp},</if>
            <if test="warnlevel != null and warnlevel != ''">warnlevel = #{warnlevel},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

</mapper>