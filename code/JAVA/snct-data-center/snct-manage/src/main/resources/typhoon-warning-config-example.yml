# 台风预警功能配置示例
# 请将以下配置添加到您的 application.yml 文件中

typhoon:
  warning:
    # 是否启用台风预警功能
    enabled: true
    
    # 是否启用定时任务调度器
    scheduler-enabled: true
    
    # 预警检查间隔（毫秒）- 默认10分钟
    check-interval: 600000
    
    # 预警去重时间窗口（毫秒）- 默认1小时
    dedup-window: 3600000
    
    # 预警半径配置（公里）
    critical-radius: 50    # 紧急预警半径
    high-radius: 100       # 高级预警半径
    medium-radius: 200     # 中级预警半径
    
    # 是否启用预警通知
    notification-enabled: true
    
    # 过期预警记录保留天数
    expired-warning-retention-days: 30

# 定时任务配置
spring:
  task:
    scheduling:
      pool:
        size: 5
      thread-name-prefix: typhoon-warning-
      
# 日志配置示例
logging:
  level:
    com.snct.typhoon.service.TyphoonWarningService: INFO
    com.snct.typhoon.scheduler.TyphoonWarningScheduler: INFO
