package com.snct.system.service.impl;

import com.snct.common.annotation.DataScope;
import com.snct.common.utils.DateUtils;
import com.snct.system.domain.msg.BuMsgPduOut;
import com.snct.system.mapper.BuMsgPduOutMapper;
import com.snct.system.service.IBuMsgPduOutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * pdu-out消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@Service
public class BuMsgPduOutServiceImpl implements IBuMsgPduOutService 
{
    @Autowired
    private BuMsgPduOutMapper buMsgPduOutMapper;

    /**
     * 查询pdu-out消息
     * 
     * @param id pdu-out消息主键
     * @return pdu-out消息
     */
    @Override
    public BuMsgPduOut selectBuMsgPduOutById(Long id)
    {
        return buMsgPduOutMapper.selectBuMsgPduOutById(id);
    }

    /**
     * 查询pdu-out消息列表
     * 
     * @param buMsgPduOut pdu-out消息
     * @return pdu-out消息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "p")
    public List<BuMsgPduOut> selectBuMsgPduOutList(BuMsgPduOut buMsgPduOut)
    {
        return buMsgPduOutMapper.selectBuMsgPduOutList(buMsgPduOut);
    }

    /**
     * 新增pdu-out消息
     * 
     * @param buMsgPduOut pdu-out消息
     * @return 结果
     */
    @Override
    public int insertBuMsgPduOut(BuMsgPduOut buMsgPduOut)
    {
        buMsgPduOut.setCreateTime(DateUtils.getNowDate());
        return buMsgPduOutMapper.insertBuMsgPduOut(buMsgPduOut);
    }

    /**
     * 修改pdu-out消息
     * 
     * @param buMsgPduOut pdu-out消息
     * @return 结果
     */
    @Override
    public int updateBuMsgPduOut(BuMsgPduOut buMsgPduOut)
    {
        return buMsgPduOutMapper.updateBuMsgPduOut(buMsgPduOut);
    }

    /**
     * 批量删除pdu-out消息
     * 
     * @param ids 需要删除的pdu-out消息主键
     * @return 结果
     */
    @Override
    public int deleteBuMsgPduOutByIds(Long[] ids)
    {
        return buMsgPduOutMapper.deleteBuMsgPduOutByIds(ids);
    }

    /**
     * 删除pdu-out消息信息
     * 
     * @param id pdu-out消息主键
     * @return 结果
     */
    @Override
    public int deleteBuMsgPduOutById(Long id)
    {
        return buMsgPduOutMapper.deleteBuMsgPduOutById(id);
    }
}
