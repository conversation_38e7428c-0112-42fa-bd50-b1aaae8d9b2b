package com.snct.system.mapper;

import java.util.List;
import com.snct.system.domain.BuShipWarning;

/**
 * 船舶预警日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface BuShipWarningMapper 
{
    /**
     * 查询船舶预警日志
     * 
     * @param id 船舶预警日志主键
     * @return 船舶预警日志
     */
    public BuShipWarning selectBuShipWarningById(Long id);

    /**
     * 查询船舶预警日志列表
     * 
     * @param buShipWarning 船舶预警日志
     * @return 船舶预警日志集合
     */
    public List<BuShipWarning> selectBuShipWarningList(BuShipWarning buShipWarning);

    /**
     * 新增船舶预警日志
     * 
     * @param buShipWarning 船舶预警日志
     * @return 结果
     */
    public int insertBuShipWarning(BuShipWarning buShipWarning);

    /**
     * 修改船舶预警日志
     * 
     * @param buShipWarning 船舶预警日志
     * @return 结果
     */
    public int updateBuShipWarning(BuShipWarning buShipWarning);

    /**
     * 删除船舶预警日志
     * 
     * @param id 船舶预警日志主键
     * @return 结果
     */
    public int deleteBuShipWarningById(Long id);

    /**
     * 批量删除船舶预警日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuShipWarningByIds(Long[] ids);
}
