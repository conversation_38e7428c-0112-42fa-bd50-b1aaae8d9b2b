package com.snct.system.service.impl;

import java.util.List;
import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuShipWarningMapper;
import com.snct.system.domain.BuShipWarning;
import com.snct.system.service.IBuShipWarningService;

/**
 * 船舶预警日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class BuShipWarningServiceImpl implements IBuShipWarningService 
{
    @Autowired
    private BuShipWarningMapper buShipWarningMapper;

    /**
     * 查询船舶预警日志
     * 
     * @param id 船舶预警日志主键
     * @return 船舶预警日志
     */
    @Override
    public BuShipWarning selectBuShipWarningById(Long id)
    {
        return buShipWarningMapper.selectBuShipWarningById(id);
    }

    /**
     * 查询船舶预警日志列表
     * 
     * @param buShipWarning 船舶预警日志
     * @return 船舶预警日志
     */
    @Override
    public List<BuShipWarning> selectBuShipWarningList(BuShipWarning buShipWarning)
    {
        return buShipWarningMapper.selectBuShipWarningList(buShipWarning);
    }

    /**
     * 新增船舶预警日志
     * 
     * @param buShipWarning 船舶预警日志
     * @return 结果
     */
    @Override
    public int insertBuShipWarning(BuShipWarning buShipWarning)
    {
        buShipWarning.setCreateTime(DateUtils.getNowDate());
        return buShipWarningMapper.insertBuShipWarning(buShipWarning);
    }

    /**
     * 修改船舶预警日志
     * 
     * @param buShipWarning 船舶预警日志
     * @return 结果
     */
    @Override
    public int updateBuShipWarning(BuShipWarning buShipWarning)
    {
        buShipWarning.setUpdateTime(DateUtils.getNowDate());
        return buShipWarningMapper.updateBuShipWarning(buShipWarning);
    }

    /**
     * 批量删除船舶预警日志
     * 
     * @param ids 需要删除的船舶预警日志主键
     * @return 结果
     */
    @Override
    public int deleteBuShipWarningByIds(Long[] ids)
    {
        return buShipWarningMapper.deleteBuShipWarningByIds(ids);
    }

    /**
     * 删除船舶预警日志信息
     * 
     * @param id 船舶预警日志主键
     * @return 结果
     */
    @Override
    public int deleteBuShipWarningById(Long id)
    {
        return buShipWarningMapper.deleteBuShipWarningById(id);
    }
}
