package com.snct.system.mapper;

import com.snct.system.domain.msg.BuMsgPduOut;

import java.util.List;

/**
 * pdu-out消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface BuMsgPduOutMapper 
{
    /**
     * 查询pdu-out消息
     * 
     * @param id pdu-out消息主键
     * @return pdu-out消息
     */
    public BuMsgPduOut selectBuMsgPduOutById(Long id);

    /**
     * 查询pdu-out消息列表
     * 
     * @param buMsgPduOut pdu-out消息
     * @return pdu-out消息集合
     */
    public List<BuMsgPduOut> selectBuMsgPduOutList(BuMsgPduOut buMsgPduOut);

    /**
     * 新增pdu-out消息
     * 
     * @param buMsgPduOut pdu-out消息
     * @return 结果
     */
    public int insertBuMsgPduOut(BuMsgPduOut buMsgPduOut);

    /**
     * 修改pdu-out消息
     * 
     * @param buMsgPduOut pdu-out消息
     * @return 结果
     */
    public int updateBuMsgPduOut(BuMsgPduOut buMsgPduOut);

    /**
     * 删除pdu-out消息
     * 
     * @param id pdu-out消息主键
     * @return 结果
     */
    public int deleteBuMsgPduOutById(Long id);

    /**
     * 批量删除pdu-out消息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuMsgPduOutByIds(Long[] ids);
}
