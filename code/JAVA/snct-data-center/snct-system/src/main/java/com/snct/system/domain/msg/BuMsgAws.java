package com.snct.system.domain.msg;


import com.snct.common.core.domain.BaseEntity;

/**
 * 气象仪自动气象站数据模型
 * 
 * 该类用于存储和处理自动气象站(AWS)的各种气象数据，包括：
 * 1. 风速风向数据 - 来自WIMWV格式的NMEA语句
 * 2. 温度、湿度、气压等传感器数据 - 来自WIXDR格式的NMEA语句
 * 3. 气压修正值(QFE/QNH)、露点数据等航空气象参数
 * 
 * 格式示例：
 * WIMWV: $WIMWV,255,R,0.9,M,A*15
 * WIXDR: $WIXDR,C,26.2,C,TA1,H,76,P,RH1,C,21.6C,DP1,P,1013.4P,PA1*79
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
public class BuMsgAws extends BaseEntity {
    
    /** 设备ID */
    private Long deviceId;
    
    /** 设备状态(0-正常) */
    private Long status = 0L;
    
    /** 相对风向角度(0-360度，0表示北，90表示东) */
    private String relativeWind;
    
    /** 相对风向标识 (R=相对值，T=理论值) */
    private String windLogoR;
    
    /** 相对风速值 */
    private String relativeWindSpeed;
    
    /** 真实风向角度(0-360度) */
    private String trueWind;
    
    /** 真实风速值 */
    private String trueWindSpeed;
    
    /** 真实风向标识 (R=相对值，T=理论值) */
    private String windLogoT;
    
    /** 风速单位 (K=千米/小时, M=米/秒, N=海里/小时) */
    private String windSpeedUnit;
    
    /** 气温传感器类型(通常为"C") */
    private String airTemType;
    
    /** 气温值(℃) */
    private String airTemperature;
    
    /** 气温单位(通常为"C") */
    private String airUnit;
    
    /** 气温传感器ID标识 */
    private String airSensor;
    
    /** 湿度传感器类型(通常为"H") */
    private String humidityType;
    
    /** 相对湿度值(%) */
    private String humidity;
    
    /** 湿度单位(通常为"%") */
    private String humidityUnit;
    
    /** 湿度传感器ID标识 */
    private String humiditySensor;
    
    /** 露点温度传感器类型(通常为"C") */
    private String pointTemType;
    
    /** 露点温度值(℃) - 当空气中的水蒸气含量达到饱和时的温度 */
    private String pointTem;
    
    /** 露点温度传感器ID标识 */
    private String pointTemSensor;
    
    /** 气压传感器类型(通常为"P") */
    private String pressureType;
    
    /** 气压值(hPa) */
    private String pressure;
    
    /** 气压单位(通常为"P"或"hPa") */
    private String pressureUnit;
    
    /** 气压传感器ID标识 */
    private String pressureSensor;
    
    /** QFE传感器类型 - 机场气压(站点气压) */
    private String qfeType;
    
    /** QFE值 - 机场气压值(hPa) */
    private String qfe;
    
    /** QFE单位(通常为"hPa") */
    private String qfeUnit;
    
    /** QFE传感器ID标识 */
    private String qfeId;
    
    /** QNH传感器类型 - 海平面气压 */
    private String qnhType;
    
    /** QNH值 - 海平面气压值(hPa) */
    private String qnh;
    
    /** QNH单位(通常为"hPa") */
    private String qnhUnit;
    
    /** QNH传感器ID标识 */
    private String qnhId;
    
    /** 露点温度传感器类型(通常为"C") - 重复字段，与pointTemType功能类似 */
    private String dpType;
    
    /** 露点温度值(℃) - 重复字段，与pointTem功能类似 */
    private String dp;
    
    /** 露点温度单位(通常为"C") */
    private String dpUnit;
    
    /** 露点温度传感器ID标识 - 重复字段，与pointTemSensor功能类似 */
    private String dpId;
    
    /** 传感器状态 (A=有效数据, V=无效数据) */
    private String sensorStatus;
    
    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getRelativeWind() {
        return this.relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return this.windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return this.relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return this.trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return this.trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return this.windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getWindSpeedUnit() {
        return this.windSpeedUnit;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }

    public String getAirTemType() {
        return this.airTemType;
    }

    public void setAirTemType(String airTemType) {
        this.airTemType = airTemType;
    }

    public String getAirTemperature() {
        return this.airTemperature;
    }

    public void setAirTemperature(String airTemperature) {
        this.airTemperature = airTemperature;
    }

    public String getAirUnit() {
        return this.airUnit;
    }

    public void setAirUnit(String airUnit) {
        this.airUnit = airUnit;
    }

    public String getAirSensor() {
        return this.airSensor;
    }

    public void setAirSensor(String airSensor) {
        this.airSensor = airSensor;
    }

    public String getHumidityType() {
        return this.humidityType;
    }

    public void setHumidityType(String humidityType) {
        this.humidityType = humidityType;
    }

    public String getHumidity() {
        return this.humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getHumidityUnit() {
        return this.humidityUnit;
    }

    public void setHumidityUnit(String humidityUnit) {
        this.humidityUnit = humidityUnit;
    }

    public String getHumiditySensor() {
        return this.humiditySensor;
    }

    public void setHumiditySensor(String humiditySensor) {
        this.humiditySensor = humiditySensor;
    }

    public String getPointTemType() {
        return this.pointTemType;
    }

    public void setPointTemType(String pointTemType) {
        this.pointTemType = pointTemType;
    }

    public String getPointTem() {
        return this.pointTem;
    }

    public void setPointTem(String pointTem) {
        this.pointTem = pointTem;
    }

    public String getPointTemSensor() {
        return this.pointTemSensor;
    }

    public void setPointTemSensor(String pointTemSensor) {
        this.pointTemSensor = pointTemSensor;
    }

    public String getPressureType() {
        return this.pressureType;
    }

    public void setPressureType(String pressureType) {
        this.pressureType = pressureType;
    }

    public String getPressure() {
        return this.pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getPressureUnit() {
        return this.pressureUnit;
    }

    public void setPressureUnit(String pressureUnit) {
        this.pressureUnit = pressureUnit;
    }

    public String getPressureSensor() {
        return this.pressureSensor;
    }

    public void setPressureSensor(String pressureSensor) {
        this.pressureSensor = pressureSensor;
    }

    public String getQfeType() {
        return this.qfeType;
    }

    public void setQfeType(String qfeType) {
        this.qfeType = qfeType;
    }

    public String getQfe() {
        return this.qfe;
    }

    public void setQfe(String qfe) {
        this.qfe = qfe;
    }

    public String getQfeUnit() {
        return this.qfeUnit;
    }

    public void setQfeUnit(String qfeUnit) {
        this.qfeUnit = qfeUnit;
    }

    public String getQfeId() {
        return this.qfeId;
    }

    public void setQfeId(String qfeId) {
        this.qfeId = qfeId;
    }

    public String getQnhType() {
        return this.qnhType;
    }

    public void setQnhType(String qnhType) {
        this.qnhType = qnhType;
    }

    public String getQnh() {
        return this.qnh;
    }

    public void setQnh(String qnh) {
        this.qnh = qnh;
    }

    public String getQnhUnit() {
        return this.qnhUnit;
    }

    public void setQnhUnit(String qnhUnit) {
        this.qnhUnit = qnhUnit;
    }

    public String getQnhId() {
        return this.qnhId;
    }

    public void setQnhId(String qnhId) {
        this.qnhId = qnhId;
    }

    public String getDpType() {
        return this.dpType;
    }

    public void setDpType(String dpType) {
        this.dpType = dpType;
    }

    public String getDp() {
        return this.dp;
    }

    public void setDp(String dp) {
        this.dp = dp;
    }

    public String getDpUnit() {
        return this.dpUnit;
    }

    public void setDpUnit(String dpUnit) {
        this.dpUnit = dpUnit;
    }

    public String getDpId() {
        return this.dpId;
    }

    public void setDpId(String dpId) {
        this.dpId = dpId;
    }
    
    public String getSensorStatus() {
        return sensorStatus;
    }

    public void setSensorStatus(String sensorStatus) {
        this.sensorStatus = sensorStatus;
    }
    
    /**
     * 转换为字符串表示
     * 
     * @return 对象的字符串表示形式
     */
    @Override
    public String toString() {
        return "BuMsgAws{" +
                "deviceId=" + deviceId +
                ", relativeWind='" + relativeWind + '\'' +
                ", relativeWindSpeed='" + relativeWindSpeed + '\'' +
                ", windSpeedUnit='" + windSpeedUnit + '\'' +
                ", airTemperature='" + airTemperature + '\'' +
                ", humidity='" + humidity + '\'' +
                ", pressure='" + pressure + '\'' +
                ", qfe='" + qfe + '\'' +
                ", qnh='" + qnh + '\'' +
                ", status=" + status +
                '}';
    }
}