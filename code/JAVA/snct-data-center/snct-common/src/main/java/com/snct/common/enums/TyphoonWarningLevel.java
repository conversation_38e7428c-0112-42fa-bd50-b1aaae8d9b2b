package com.snct.common.enums;

/**
 * 台风预警级别枚举
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
public enum TyphoonWarningLevel {
    
    /**
     * 紧急预警 - 50km内
     */
    CRITICAL(1, 50, "紧急预警", "台风中心50公里范围内，风险极高，请立即采取避险措施！"),
    
    /**
     * 高级预警 - 100km内
     */
    HIGH(2, 100, "高级预警", "台风中心100公里范围内，风险较高，请密切关注并做好防护准备！"),
    
    /**
     * 中级预警 - 200km内
     */
    MEDIUM(3, 200, "中级预警", "台风中心200公里范围内，请注意台风动向，做好预防措施！");
    
    /**
     * 数据库存储的级别值
     */
    private final int dbLevel;
    
    /**
     * 预警半径（公里）
     */
    private final double radiusKm;
    
    /**
     * 预警级别描述
     */
    private final String description;
    
    /**
     * 预警详细信息
     */
    private final String detailMessage;
    
    TyphoonWarningLevel(int dbLevel, double radiusKm, String description, String detailMessage) {
        this.dbLevel = dbLevel;
        this.radiusKm = radiusKm;
        this.description = description;
        this.detailMessage = detailMessage;
    }
    
    public int getDbLevel() {
        return dbLevel;
    }
    
    public double getRadiusKm() {
        return radiusKm;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getDetailMessage() {
        return detailMessage;
    }
    
    /**
     * 根据距离确定预警级别
     * 
     * @param distance 距离（公里）
     * @return 预警级别，如果超出预警范围则返回null
     */
    public static TyphoonWarningLevel determineByDistance(double distance) {
        if (distance <= CRITICAL.radiusKm) {
            return CRITICAL;
        }
        if (distance <= HIGH.radiusKm) {
            return HIGH;
        }
        if (distance <= MEDIUM.radiusKm) {
            return MEDIUM;
        }
        return null; // 超出预警范围
    }
    
    /**
     * 根据数据库级别值获取枚举
     * 
     * @param dbLevel 数据库级别值
     * @return 预警级别枚举
     */
    public static TyphoonWarningLevel getByDbLevel(int dbLevel) {
        for (TyphoonWarningLevel level : values()) {
            if (level.dbLevel == dbLevel) {
                return level;
            }
        }
        throw new IllegalArgumentException("未知的预警级别: " + dbLevel);
    }
    
    /**
     * 获取最大预警半径
     * 
     * @return 最大预警半径（公里）
     */
    public static double getMaxRadius() {
        return MEDIUM.radiusKm;
    }
}
