package com.snct.common.domain;

import com.snct.common.enums.TyphoonWarningLevel;
import org.springframework.data.geo.Point;

/**
 * 台风船舶预警信息
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
public class TyphoonShipWarning {
    
    /**
     * 船舶SN号
     */
    private String shipSn;
    
    /**
     * 船舶名称
     */
    private String shipName;
    
    /**
     * 距离台风中心的距离（公里）
     */
    private double distance;
    
    /**
     * 预警级别
     */
    private TyphoonWarningLevel level;
    
    /**
     * 船舶位置坐标
     */
    private Point shipLocation;
    
    /**
     * 台风ID
     */
    private String typhoonId;
    
    /**
     * 台风名称
     */
    private String typhoonName;
    
    /**
     * 台风中心位置
     */
    private Point typhoonLocation;
    
    /**
     * 预警时间戳
     */
    private long warningTime;
    
    public TyphoonShipWarning() {
        this.warningTime = System.currentTimeMillis();
    }
    
    public TyphoonShipWarning(String shipSn, double distance, TyphoonWarningLevel level, Point shipLocation) {
        this();
        this.shipSn = shipSn;
        this.distance = distance;
        this.level = level;
        this.shipLocation = shipLocation;
    }
    
    public TyphoonShipWarning(String shipSn, String shipName, double distance, 
                             TyphoonWarningLevel level, Point shipLocation,
                             String typhoonId, String typhoonName, Point typhoonLocation) {
        this();
        this.shipSn = shipSn;
        this.shipName = shipName;
        this.distance = distance;
        this.level = level;
        this.shipLocation = shipLocation;
        this.typhoonId = typhoonId;
        this.typhoonName = typhoonName;
        this.typhoonLocation = typhoonLocation;
    }
    
    /**
     * 生成预警消息
     * 
     * @return 预警消息文本
     */
    public String generateWarningMessage() {
        return String.format(
            "%s：%s(%s) 距离台风 %s 约 %.1f 公里，%s",
            level.getDescription(),
            shipName != null ? shipName : "未知船舶",
            shipSn,
            typhoonName != null ? typhoonName : "未知台风",
            distance,
            level.getDetailMessage()
        );
    }
    
    /**
     * 生成数据键（用于存储到预警表的dataKey字段）
     * 
     * @return 数据键字符串
     */
    public String generateDataKey() {
        return String.format(
            "TYPHOON:%s|DISTANCE:%.1f|SHIP_LAT:%.6f|SHIP_LNG:%.6f|TYPHOON_LAT:%.6f|TYPHOON_LNG:%.6f|LEVEL:%s|TIME:%d",
            typhoonId != null ? typhoonId : "UNKNOWN",
            distance,
            shipLocation != null ? shipLocation.getY() : 0.0,
            shipLocation != null ? shipLocation.getX() : 0.0,
            typhoonLocation != null ? typhoonLocation.getY() : 0.0,
            typhoonLocation != null ? typhoonLocation.getX() : 0.0,
            level.name(),
            warningTime
        );
    }
    
    // Getter and Setter methods
    
    public String getShipSn() {
        return shipSn;
    }
    
    public void setShipSn(String shipSn) {
        this.shipSn = shipSn;
    }
    
    public String getShipName() {
        return shipName;
    }
    
    public void setShipName(String shipName) {
        this.shipName = shipName;
    }
    
    public double getDistance() {
        return distance;
    }
    
    public void setDistance(double distance) {
        this.distance = distance;
    }
    
    public TyphoonWarningLevel getLevel() {
        return level;
    }
    
    public void setLevel(TyphoonWarningLevel level) {
        this.level = level;
    }
    
    public Point getShipLocation() {
        return shipLocation;
    }
    
    public void setShipLocation(Point shipLocation) {
        this.shipLocation = shipLocation;
    }
    
    public String getTyphoonId() {
        return typhoonId;
    }
    
    public void setTyphoonId(String typhoonId) {
        this.typhoonId = typhoonId;
    }
    
    public String getTyphoonName() {
        return typhoonName;
    }
    
    public void setTyphoonName(String typhoonName) {
        this.typhoonName = typhoonName;
    }
    
    public Point getTyphoonLocation() {
        return typhoonLocation;
    }
    
    public void setTyphoonLocation(Point typhoonLocation) {
        this.typhoonLocation = typhoonLocation;
    }
    
    public long getWarningTime() {
        return warningTime;
    }
    
    public void setWarningTime(long warningTime) {
        this.warningTime = warningTime;
    }
    
    @Override
    public String toString() {
        return "TyphoonShipWarning{" +
                "shipSn='" + shipSn + '\'' +
                ", shipName='" + shipName + '\'' +
                ", distance=" + distance +
                ", level=" + level +
                ", typhoonId='" + typhoonId + '\'' +
                ", typhoonName='" + typhoonName + '\'' +
                ", warningTime=" + warningTime +
                '}';
    }
}
